// 浏览器端代理客户端
const Logger = {
    enabled: true,
    logDiv: null,
    logQueue: [],
    maxLogs: 100, // 限制日志条数防止内存泄漏
    batchSize: 5,
    updateTimer: null,

    init() {
        if (this.logDiv) return this.logDiv;

        this.logDiv = document.createElement('div');
        this.logDiv.id = 'proxy-logs';
        this.logDiv.style.cssText = `
            position: fixed; top: 10px; left: 10px; width: 400px; height: 200px;
            background: rgba(0,0,0,0.8); color: #00ff00; font-family: monospace;
            font-size: 12px; padding: 10px; overflow-y: auto; z-index: 9999;
            border-radius: 5px;
        `;
        document.body.appendChild(this.logDiv);
        return this.logDiv;
    },

    output(...messages) {
        if (!this.enabled) return;

        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}]`, ...messages);

        // 批量处理日志以提高性能
        this.logQueue.push({
            timestamp,
            message: messages.join(' ')
        });

        // 防抖更新DOM
        if (this.updateTimer) {
            clearTimeout(this.updateTimer);
        }

        this.updateTimer = setTimeout(() => {
            this.flushLogs();
        }, 16); // ~60fps
    },

    flushLogs() {
        if (this.logQueue.length === 0) return;

        const logDiv = this.init();
        const fragment = document.createDocumentFragment();

        // 批量处理日志条目
        while (this.logQueue.length > 0) {
            const batch = this.logQueue.splice(0, this.batchSize);

            batch.forEach(({ timestamp, message }) => {
                const logLine = document.createElement('div');
                logLine.textContent = `[${timestamp}] ${message}`;
                fragment.appendChild(logLine);
            });
        }

        logDiv.appendChild(fragment);

        // 限制日志条数，移除旧日志
        const children = logDiv.children;
        if (children.length > this.maxLogs) {
            const removeCount = children.length - this.maxLogs;
            for (let i = 0; i < removeCount; i++) {
                logDiv.removeChild(children[0]);
            }
        }

        logDiv.scrollTop = logDiv.scrollHeight;
    },

    clear() {
        if (this.logDiv) {
            this.logDiv.innerHTML = '';
        }
        this.logQueue = [];
    }
};

class ProxySystem {
    constructor(spaceUrl) {
        this.spaceUrl = spaceUrl;
        this.wsUrl = spaceUrl.replace('https://', 'wss://'); // 缓存WebSocket URL
        this.mainSocket = null;
        this.requestSockets = new Map();
        this.isConnected = false;
        this.connectionPool = new Map(); // 连接池
        this.maxPoolSize = 10;
        this.cleanupInterval = null;
        this.messageQueue = []; // 消息队列
        this.processingQueue = false;

        // 性能监控
        this.stats = {
            requestsProcessed: 0,
            connectionsCreated: 0,
            avgResponseTime: 0,
            lastCleanup: Date.now()
        };
    }

    async initialize() {
        Logger.output('🚀 初始化代理系统...');

        return new Promise((resolve, reject) => {
            try {
                this.mainSocket = new WebSocket(`${this.wsUrl}/ws`);

                this.mainSocket.onopen = () => {
                    this.isConnected = true;
                    Logger.output('✅ 主连接建立成功');
                    this.startCleanupTimer();
                    resolve();
                };

                this.mainSocket.onmessage = (event) => {
                    this.queueMessage(event.data);
                };

                this.mainSocket.onerror = (error) => {
                    Logger.output('❌ 连接错误:', error);
                    this.cleanup();
                    reject(error);
                };

                this.mainSocket.onclose = () => {
                    this.isConnected = false;
                    Logger.output('🔌 连接断开');
                    this.cleanup();
                };
            } catch (error) {
                Logger.output('❌ 初始化失败:', error);
                reject(error);
            }
        });
    }

    // 消息队列处理，避免阻塞
    queueMessage(data) {
        this.messageQueue.push(data);
        if (!this.processingQueue) {
            this.processMessageQueue();
        }
    }

    async processMessageQueue() {
        this.processingQueue = true;

        while (this.messageQueue.length > 0) {
            const data = this.messageQueue.shift();
            try {
                await this.handleMainMessage(data);
            } catch (error) {
                Logger.output('❌ 消息处理错误:', error);
            }

            // 让出控制权，避免阻塞UI
            if (this.messageQueue.length > 0) {
                await new Promise(resolve => setTimeout(resolve, 0));
            }
        }

        this.processingQueue = false;
    }

    // 定期清理过期连接
    startCleanupTimer() {
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpiredConnections();
        }, 30000); // 每30秒清理一次
    }

    cleanupExpiredConnections() {
        const now = Date.now();
        const expireTime = 60000; // 60秒过期

        for (const [requestId, socketInfo] of this.requestSockets.entries()) {
            if (now - socketInfo.created > expireTime) {
                Logger.output(`🧹 清理过期连接 [${requestId}]`);
                socketInfo.socket.close();
                this.requestSockets.delete(requestId);
            }
        }

        this.stats.lastCleanup = now;
    }

    cleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }

        // 关闭所有连接
        for (const socketInfo of this.requestSockets.values()) {
            socketInfo.socket.close();
        }
        this.requestSockets.clear();
        this.connectionPool.clear();
        this.messageQueue = [];
    }

    async handleMainMessage(data) {
        let message;
        try {
            message = JSON.parse(data);
        } catch (error) {
            Logger.output('❌ JSON解析错误:', error);
            return;
        }

        const requestId = message.request_id;
        if (!requestId) {
            Logger.output('❌ 缺少请求ID');
            return;
        }

        const startTime = performance.now();
        Logger.output(`📨 收到请求 [${requestId}]: ${message.method} ${message.path}`);

        try {
            // 创建请求专用连接
            await this.createRequestConnection(requestId);

            // 处理实际请求
            await this.processRequest(message);

            // 更新统计信息
            this.stats.requestsProcessed++;
            const responseTime = performance.now() - startTime;
            this.stats.avgResponseTime = (this.stats.avgResponseTime + responseTime) / 2;

        } catch (error) {
            Logger.output(`❌ 请求处理失败 [${requestId}]:`, error);
        }
    }

    async createRequestConnection(requestId) {
        // 检查是否已存在连接
        if (this.requestSockets.has(requestId)) {
            return this.requestSockets.get(requestId).socket;
        }

        return new Promise((resolve, reject) => {
            const socket = new WebSocket(`${this.wsUrl}/ws/request/${requestId}`);
            const socketInfo = {
                socket,
                created: Date.now(),
                requestId
            };

            const timeout = setTimeout(() => {
                socket.close();
                reject(new Error(`连接超时 [${requestId}]`));
            }, 10000); // 10秒超时

            socket.onopen = () => {
                clearTimeout(timeout);
                this.requestSockets.set(requestId, socketInfo);
                this.stats.connectionsCreated++;
                Logger.output(`🔗 请求连接建立 [${requestId}]`);
                resolve(socket);
            };

            socket.onerror = (error) => {
                clearTimeout(timeout);
                Logger.output(`❌ 请求连接错误 [${requestId}]:`, error);
                reject(error);
            };

            socket.onclose = () => {
                clearTimeout(timeout);
                this.requestSockets.delete(requestId);
            };
        });
    }

    async processRequest(requestSpec) {
        const requestId = requestSpec.request_id;
        const socketInfo = this.requestSockets.get(requestId);

        if (!socketInfo) {
            Logger.output(`❌ 找不到连接 [${requestId}]`);
            return;
        }

        const socket = socketInfo.socket;
        let reader = null;

        try {
            // 构建请求URL - 优化字符串操作
            const baseUrl = `https://generativelanguage.googleapis.com${requestSpec.path}`;
            let fullUrl = baseUrl;

            if (requestSpec.query_params && Object.keys(requestSpec.query_params).length > 0) {
                const queryString = new URLSearchParams(requestSpec.query_params).toString();
                fullUrl = `${baseUrl}?${queryString}`;
            }

            Logger.output(`🌐 发送请求: ${requestSpec.method} ${fullUrl}`);

            // 优化请求头处理 - 使用解构和过滤
            const { host, origin, referer, ...cleanHeaders } = requestSpec.headers || {};

            // 发送请求
            const response = await fetch(fullUrl, {
                method: requestSpec.method,
                headers: cleanHeaders,
                body: requestSpec.body || undefined
            });

            Logger.output(`📥 收到响应: ${response.status} ${response.statusText}`);

            // 优化响应头处理 - 使用Object.fromEntries
            const responseHeaders = Object.fromEntries(response.headers.entries());

            // 批量发送消息以减少WebSocket开销
            const headerMessage = {
                request_id: requestId,
                event_type: 'response_headers',
                status: response.status,
                headers: responseHeaders
            };

            this.sendMessage(socket, headerMessage);

            // 优化流式处理
            if (response.body) {
                reader = response.body.getReader();
                const decoder = new TextDecoder();
                let chunkBuffer = [];
                const maxBufferSize = 5; // 批量发送减少WebSocket调用

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    chunkBuffer.push(chunk);

                    // 批量发送chunk以提高性能
                    if (chunkBuffer.length >= maxBufferSize) {
                        this.sendMessage(socket, {
                            request_id: requestId,
                            event_type: 'chunk',
                            data: chunkBuffer.join('')
                        });
                        chunkBuffer = [];
                    }
                }

                // 发送剩余的chunk
                if (chunkBuffer.length > 0) {
                    this.sendMessage(socket, {
                        request_id: requestId,
                        event_type: 'chunk',
                        data: chunkBuffer.join('')
                    });
                }
            }

            // 发送结束信号
            this.sendMessage(socket, {
                request_id: requestId,
                event_type: 'stream_close'
            });

            Logger.output(`✅ 请求完成 [${requestId}]`);

        } catch (error) {
            Logger.output(`❌ 请求处理错误 [${requestId}]:`, error.message);

            this.sendMessage(socket, {
                request_id: requestId,
                event_type: 'error',
                status: 500,
                message: error.message
            });
        } finally {
            // 清理资源
            if (reader) {
                try {
                    reader.releaseLock();
                } catch (e) {
                    // 忽略释放锁的错误
                }
            }

            // 延迟清理连接，但使用更短的延迟
            setTimeout(() => {
                if (socket.readyState === WebSocket.OPEN) {
                    socket.close();
                }
                this.requestSockets.delete(requestId);
            }, 100); // 减少到100ms
        }
    }

    // 优化消息发送，添加错误处理
    sendMessage(socket, message) {
        try {
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify(message));
            } else {
                Logger.output(`❌ WebSocket连接已关闭，无法发送消息 [${message.request_id}]`);
            }
        } catch (error) {
            Logger.output(`❌ 发送消息失败 [${message.request_id}]:`, error.message);
        }
    }
}

// 性能监控和状态管理
const StatusManager = {
    statusDiv: null,
    statsDiv: null,

    createStatusIndicator(isConnected, proxy = null) {
        // 移除旧的状态指示器
        if (this.statusDiv) {
            this.statusDiv.remove();
        }

        this.statusDiv = document.createElement('div');
        this.statusDiv.style.cssText = `
            position: fixed; top: 10px; right: 10px;
            background: ${isConnected ? '#28a745' : '#dc3545'};
            color: white; padding: 10px; border-radius: 5px;
            font-weight: bold; z-index: 10000; cursor: pointer;
        `;
        this.statusDiv.textContent = isConnected ? '🟢 代理已连接' : '🔴 代理连接失败';

        // 添加点击事件显示统计信息
        if (isConnected && proxy) {
            this.statusDiv.addEventListener('click', () => {
                this.toggleStats(proxy);
            });
        }

        document.body.appendChild(this.statusDiv);
    },

    toggleStats(proxy) {
        if (this.statsDiv) {
            this.statsDiv.remove();
            this.statsDiv = null;
            return;
        }

        this.statsDiv = document.createElement('div');
        this.statsDiv.style.cssText = `
            position: fixed; top: 60px; right: 10px; width: 300px;
            background: rgba(0,0,0,0.9); color: #00ff00;
            padding: 15px; border-radius: 5px; font-family: monospace;
            font-size: 12px; z-index: 10001;
        `;

        this.updateStats(proxy);
        document.body.appendChild(this.statsDiv);

        // 定期更新统计信息
        const updateInterval = setInterval(() => {
            if (this.statsDiv) {
                this.updateStats(proxy);
            } else {
                clearInterval(updateInterval);
            }
        }, 1000);
    },

    updateStats(proxy) {
        if (!this.statsDiv) return;

        const stats = proxy.stats;
        const uptime = Math.floor((Date.now() - stats.lastCleanup) / 1000);

        this.statsDiv.innerHTML = `
            <div><strong>📊 代理系统统计</strong></div>
            <div>运行时间: ${uptime}s</div>
            <div>处理请求: ${stats.requestsProcessed}</div>
            <div>创建连接: ${stats.connectionsCreated}</div>
            <div>活跃连接: ${proxy.requestSockets.size}</div>
            <div>平均响应时间: ${stats.avgResponseTime.toFixed(2)}ms</div>
            <div>消息队列: ${proxy.messageQueue.length}</div>
            <div style="margin-top: 10px; font-size: 10px; color: #888;">
                点击状态指示器关闭
            </div>
        `;
    }
};

// 启动代理系统
async function startProxy() {
    const spaceUrl = 'https://youyushi-proxysystem.hf.space';
    const proxy = new ProxySystem(spaceUrl);

    try {
        const startTime = performance.now();
        await proxy.initialize();
        const initTime = performance.now() - startTime;

        window.proxySystem = proxy;
        Logger.output(`🎉 代理系统启动成功！初始化耗时: ${initTime.toFixed(2)}ms`);

        StatusManager.createStatusIndicator(true, proxy);

        // 添加全局错误处理
        window.addEventListener('beforeunload', () => {
            proxy.cleanup();
        });

        // 添加性能监控
        if (typeof PerformanceObserver !== 'undefined') {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name.includes('fetch')) {
                        Logger.output(`⚡ 网络请求: ${entry.duration.toFixed(2)}ms`);
                    }
                }
            });
            observer.observe({ entryTypes: ['measure', 'navigation'] });
        }

    } catch (error) {
        Logger.output('❌ 代理系统启动失败:', error);
        StatusManager.createStatusIndicator(false);
    }
}

// 添加调试和控制功能
window.ProxyDebug = {
    clearLogs: () => Logger.clear(),
    getStats: () => window.proxySystem?.stats,
    reconnect: async () => {
        if (window.proxySystem) {
            window.proxySystem.cleanup();
        }
        await startProxy();
    },
    toggleLogging: () => {
        Logger.enabled = !Logger.enabled;
        Logger.output(`日志记录已${Logger.enabled ? '启用' : '禁用'}`);
    }
};

// 立即启动
startProxy();